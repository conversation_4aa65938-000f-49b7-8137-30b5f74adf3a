# AugmentCode-Free v2.0.0 (Click the star，continuously updating and maintaining)
**点个星标，持续更新维护中……**
# Telegram Communication Group

https://t.me/+rL2-my2wYy5hODA1
![0d0f761c0d09056358a24766f806802](https://github.com/user-attachments/assets/64c8cb5d-7989-4c8e-808b-0b72b6cc9cc8)





**支持多IDE**: VS Code、Cursor、Windsurf、JetBrains 的专业维护工具包

**Multi-IDE Support**: Maintenance toolkit for VS Code, Cursor, Windsurf, and JetBrains

[![GitHub release](https://img.shields.io/github/v/release/BasicProtein/AugmentCode-Free)](https://github.com/BasicProtein/AugmentCode-Free/releases)
[![Python](https://img.shields.io/badge/Python-3.7+-blue.svg)](https://www.python.org/downloads/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Platform](https://img.shields.io/badge/Platform-Windows%20%7C%20Linux%20%7C%20macOS-lightgrey.svg)](https://github.com/BasicProtein/AugmentCode-Free)

#### 2025年8月22日更新 (v2.0.4)：

**🔧 构建优化版 / Build Optimized Version**：

**核心功能改进 / Core Feature Improvements:**
- **权限检查增强 / Enhanced Permission Check**：新增文件只读状态检测，提供友好的权限错误提示 / Added read-only file detection with friendly permission error messages
- **国际化完善 / Improved Internationalization**：错误信息支持中英文双语显示，包含详细解决方案 / Error messages support bilingual display with detailed solutions
- **用户体验提升 / Enhanced User Experience**：当补丁失败时，自动检测是否为只读文件并提供具体操作指导 / Automatically detects read-only files when patching fails and provides specific guidance
- **错误处理优化 / Optimized Error Handling**：区分权限问题和其他写入错误，提供针对性的解决建议 / Distinguishes permission issues from other write errors with targeted solutions

**macOS 构建修复 / macOS Build Fixes:**
- **构建问题修复 / Build Issues Fixed**：解决 "Failed to create parent directory structure" 错误 / Fixed "Failed to create parent directory structure" error
- **双版本支持 / Dual Version Support**：提供应用包(.app)和独立可执行文件两个版本 / Provides both app bundle (.app) and standalone executable versions
- **权限优化 / Permission Optimization**：自动处理 macOS 权限和安全设置 / Automatically handles macOS permissions and security settings
- **架构支持 / Architecture Support**：支持 Intel 和 Apple Silicon 架构 / Supports Intel and Apple Silicon architectures

**文档完善 / Documentation Improvements:**
- **构建指南 / Build Guide**：更新 macOS 构建说明，包含详细的故障排除 / Updated macOS build instructions with detailed troubleshooting
- **故障排除 / Troubleshooting**：新增专用的 [macOS 故障排除指南](https://github.com/BasicProtein/AugmentCode-Free/blob/main/docs/MACOS_TROUBLESHOOTING.md) / Added dedicated [macOS Troubleshooting Guide](https://github.com/BasicProtein/AugmentCode-Free/blob/main/docs/MACOS_TROUBLESHOOTING.md)
- **自动化脚本 / Automation Scripts**：提供 `build_macos.sh` 自动构建脚本 / Provides `build_macos.sh` automated build script

#### 2025年8月14日更新 (v2.0.2)：

**🔍 扩展文件扫描优化**：
- **路径模式增强**：添加 `augment.vscode-augment-*` 等多种扩展名称模式，提升扫描成功率
- **搜索策略改进**：当标准搜索失败时，自动启用关键词搜索作为备选方案
- **文件验证优化**：支持识别压缩/混淆的扩展文件，基于文件路径进行智能判断
- **调试信息增强**：添加详细的搜索过程日志，帮助定位扫描问题
- **全IDE支持**：优化覆盖 VS Code、Cursor、Windsurf 等所有支持的IDE

**🔍 Extension File Scanning Optimization**：
- **Enhanced Path Patterns**: Added multiple extension name patterns like `augment.vscode-augment-*` to improve scan success rate
- **Improved Search Strategy**: Automatically enables keyword search as fallback when standard search fails
- **Optimized File Validation**: Supports recognition of compressed/obfuscated extension files with intelligent path-based judgment
- **Enhanced Debug Information**: Added detailed search process logs to help locate scanning issues
- **Full IDE Support**: Optimization covers all supported IDEs including VS Code, Cursor, Windsurf

#### 2025年8月12日更新 (v2.0.0)：

**🚀 重大功能升级 - 增强清理引擎**：
- **强力进程管理**：智能检测和终止IDE进程，支持多种终止策略和重试机制
- **高级文件清理**：物理文件删除功能，支持强制删除被锁定的文件
- **多种清理模式**：
  - `database_only`: 仅清理数据库内容，保留文件结构
  - `file_only`: 仅删除物理文件，不修改数据库内容
  - `hybrid`: 推荐模式，先清理数据库内容，再删除相关文件
  - `aggressive`: 激进模式，强制终止进程并删除所有相关文件
- **增强CLI命令**：新增 `clean-enhanced`, `check-processes`, `kill-processes`, `file-cleanup` 命令
- **自动备份**：清理前自动创建数据库备份，确保数据安全
- **详细统计**：显示删除的具体条目数、文件数和进程数
- **完全向后兼容**：现有GUI界面保持不变，后端功能大幅增强

#### Updated on August 12, 2025 (v2.0.0):

**🔧 Major Feature Upgrade - Enhanced Cleanup Engine**：
- **Powerful Process Management**: Intelligent IDE process detection and termination with multiple strategies
- **Advanced File Cleanup**: Physical file deletion with force delete for locked files
- **Multiple Cleanup Modes**: database_only, file_only, hybrid, and aggressive modes
- **Enhanced CLI Commands**: New commands for comprehensive cleanup operations
- **Automatic Backup**: Auto-backup database before cleanup for data safety
- **Detailed Statistics**: Shows exact counts of deleted entries, files, and processes
- **Full Backward Compatibility**: Existing GUI unchanged, backend significantly enhanced

**🎨 界面优化与多语言完善**：
- **完美多语言支持**：所有界面元素支持中英文实时切换，包括按钮、标签、下拉框选项
- **布局优化**：窗口宽度增加至680px，按钮宽度全面提升，确保英文文本完整显示
- **补丁功能集成**：新增代码补丁应用、恢复、扫描功能，支持多种补丁模式（随机假数据、完全阻止、空数据、隐身模式、调试模式）
- **界面细节优化**：清空日志按钮定位优化，垂直高度增加，解决显示截断问题
- **用户体验提升**：移除多余emoji，界面更简洁专业，所有按钮和文本在中英文模式下都能完美显示

**🎨 Interface Optimization & Multilingual Enhancement**：
- **Perfect Multilingual Support**: All interface elements support real-time Chinese-English switching, including buttons, labels, and dropdown options
- **Layout Optimization**: Window width increased to 680px, button widths comprehensively enhanced to ensure complete English text display
- **Patch Function Integration**: Added code patch application, restoration, and scanning features with multiple patch modes (random fake data, complete block, empty data, stealth mode, debug mode)
- **Interface Detail Optimization**: Clear log button positioning optimized, vertical height increased, resolving display truncation issues
- **User Experience Enhancement**: Removed redundant emojis for a cleaner, more professional interface; all buttons and text display perfectly in both Chinese and English modes

#### 2025年8月5日更新：

- 分享一个Augment魔改去风控版本，只要注册直接登录即可过风控直接使用，已经稳定二天半，具体核心原理就是去掉登录之后的上报接口。
- [Augment-521.vsix.zip](https://sharewh.chaoxing.com/share/download/83b70fb33c370dee593e52475d10e8e6)
- 作者@待我绝境涅槃
- [详细的使用方法](https://github.com/agassiz/vscode-augment/blob/main/README.md)

#### Updated on August 5, 2025:

- I'd like to share a modified version of Augment that removes risk control. You can use it directly by simply registering and logging in, passing risk control. It has been stable for two and a half days. The core principle is to remove the reporting interface after login.
- [Augment-521.vsix.zip](https://sharewh.chaoxing.com/share/download/83b70fb33c370dee593e52475d10e8e6)
- Author@待我绝境涅槃
-  [Detailed usage instructions](https://github.com/agassiz/vscode-augment/blob/main/README.md)
-  [Installation Instructions (English version)](https://github.com/BasicProtein/AugmentCode-Free/blob/master/Installation-Method-EN.md)




---
AugmentCode无限免费续杯方案；新账号可获得600次免费的Claude Sonnet 4调用

AugmentCode unlimited free refill plan; new accounts can get 600 free Claude Sonnet 4 calls

## 使用工具后隔了半小时或者几个小时后封号，这种情况一般与网络环境也有关，与本工具无关，因为本项目原理无非是伪装成新用户无限合法使用
## If the account is blocked half an hour or several hours after using the tool, this situation is generally related to the network environment and has nothing to do with this tool, because the principle of this project is nothing more than pretending to be a new user and using it legally indefinitely.

#### 2025年8月1日更新：
- 目前社区计划已经失效
- 请使用GCP方式，配合多IDE使用（Windsurf、Cursor、VSCode、Kiro、Void、IDEA、Android Studio分别安装Augment插件，然后多账号免费试用，一个账号125次免费请求，7个IDE就是7*125=875次免费请求）
- 当所有IDE均换过一轮账号时，重置一下vm实例，重复循环上述操作


#### Updated on August 1, 2025:
 - The community plan is no longer valid.
 - Please use the GCP method in conjunction with multiple IDEs (Windsurf, Cursor, VSCode, Kiro, Void, IDEA, and Android Studio). Install the Augment plugin on each IDE. Use multiple accounts for free trials; each account offers 125 free requests. With 7 IDEs, this gives you a total of 7 * 125 = 875 free requests.
 - Once you've cycled through all the accounts on every IDE, simply reset the virtual machine (VM) instance and repeat the process.



#### 2025年7月31日更新：
- 目前比较稳定的计划是切换社区计划或者是通过Google的GCP或者Github Actions间接无限使用新账号
- 详见YouTube视频[Google Cloud Platform](https://youtu.be/P2ADJdk5mYo?si=2NgH6qIHP7BBEUUf)


**切换社区计划：**
- 首先在干净的环境下注册一个全新的账号，可能部分用户注册的时候会遇到问题，这时候建议使用指纹浏览器，然后开启全局模式的VPN工具，或者开启TUN模式
- 注册完账户选择计划，切换为社区计划，如下图
![IMG_2761](https://github.com/user-attachments/assets/86ef0037-aa9d-48c3-8c40-3001e965de85)
- 切换完成之后，执行一次清理工具，然后登录账号即可使用，次数用完之后再循环按照步骤执行一般更换账号

**Google Cloud Platform&Github Actions**
- 这种方式本质上是借用第三方平台提供的虚拟机以达成伪装成新用户的方式
- 每次更换账号只要重置虚拟设备即可
- 参考YouTube视频[Google Cloud Platform](https://youtu.be/P2ADJdk5mYo?si=2NgH6qIHP7BBEUUf)


**常见问题与解决方案：**
 * 问题： "<NAME_EMAIL> has been suspended. To continue, purchase a subscription."
   * 解决方案： 在您的 VPN 客户端上启用全局模式。
 * 问题： "Due to increased demand, we're limiting signups in certain regions to maintain performance for existing customers."
   * 解决方案： 使用防关联浏览器（也称指纹浏览器）或其他类型的隐身浏览器，并测试您的 IP 地址。
 * 问题： A "No Auth..." error message pops up in the VS Code IDE after logging in via a web browser.
   * 解决方案： 使用防关联浏览器或其他隐身浏览器。
   * 额外提示： 在接收验证码之前，您可以将 VPN 工具设置为规则模式。注册和登录过程也可以使用两个不同的 IP 地址。
 * 问题： "Change your subscription. Switch plans or contact sales about Enterprise options."
   * 解决方案： 清理您的浏览器环境（例如，缓存、Cookie 和本地存储）。
  

**推荐的防关联浏览器：**
 * RoxyBrowser: https://roxybrowser.com
 * AdsPower: https://www.adspower.net/


#### Updated on July 31, 2025:
 * Currently, the more stable plans are to switch to the Community Plan or to indirectly use new accounts indefinitely through Google's GCP or GitHub Actions.
 * Refer to this YouTube video: [Google Cloud Platform](https://youtu.be/P2ADJdk5mYo?si=2NgH6qIHP7BBEUUf)


**Switching to the Community Plan:**
 * First, register a new account in a clean environment. Some users may encounter issues during registration. In this case, it is recommended to use an anti-detect browser and enable a global mode VPN or TUN mode.
 * After registering the account, select a plan and switch to the Community Plan, as shown in the image below.
 * After switching, run a cleaning tool, then log in to the account to use it. After the usage is exhausted, repeat the steps to change the account.


**Google Cloud Platform & GitHub Actions**
 * This method essentially uses virtual machines provided by third-party platforms to masquerade as a new user.
 * Each time you change your account, you only need to reset the virtual device.
 * Refer to this YouTube video: [Google Cloud Platform](https://youtu.be/P2ADJdk5mYo?si=2NgH6qIHP7BBEUUf)

**Common Issues and Solutions:**
 * Issue: "<NAME_EMAIL> has been suspended. To continue, purchase a subscription."
   * Solution: Enable global mode on your VPN client.
 * Issue: "Due to increased demand, we're limiting signups in certain regions to maintain performance for existing customers."
   * Solution: Use an anti-detect browser (also known as a fingerprint browser) or another type of stealth browser, and test your IP address.
 * Issue: A "No Auth..." error message pops up in the VS Code IDE after logging in via a web browser.
   * Solution: Use an anti-detect browser or another stealth browser.
   * Additional Tip: Before receiving the verification code, you can set your VPN tool to rule-based mode. It's also possible to use two different IP addresses for the registration and login processes.
 * Issue: "Change your subscription. Switch plans or contact sales about Enterprise options."
   * Solution: Clear your Browse environment (e.g., cache, cookies, and local storage).
  


**Recommended Anti-Detect Browsers:**
 * RoxyBrowser: https://roxybrowser.com
 * AdsPower: https://www.adspower.net/



#### 2025年7月27日更新 (v1.0.6)：

**🔧 macOS路径修正**：
- 修正了Cursor在macOS上的路径配置问题
- 统一了所有IDE在macOS上的标准应用数据存储路径
- 确保清理工具能够正确定位Cursor数据文件
- Fixed the path configuration issue of Cursor on macOS
- Unified the standard application data storage path for all IDEs on macOS
- Ensured that the cleanup tool can correctly locate the Cursor data file

**📢 重要更新**：
- 目前最新逆向思路已经小范围测试成功；未来可能在合适时机发布；但是该逆向方式已经严重侵害了官方的正常运行权益，需要慎重考虑
- Alright, here's the deal. The new back-end workaround? We ran a little test, and boom, it works. We're thinking about dropping it, maybe when the time's right. But here's the kicker: this thing totally screws over the main guys, I mean, it's messing with their whole operation, big time. So, yeah, we gotta think this through. This ain't no joke.
- 目前邀请策略已经接近全面失效，可以通过换号+清理环境方式无限使用
- The current invitation strategy is almost completely ineffective. You can use it unlimitedly by changing your account number and cleaning up the environment.
- 使用工具后隔了半小时或者几个小时后封号，这种情况一般与网络环境也有关，与本工具无关，因为本项目原理无非是伪装成新用户无限合法使用
- If the account is blocked half an hour or several hours after using the tool, this situation is generally related to the network environment and has nothing to do with this tool, because the principle of this project is nothing more than pretending to be a new user and using it legally indefinitely.

<img width="828" height="565" alt="image" src="https://github.com/user-attachments/assets/af42e76a-d438-4c80-890a-7fec1dc338f9" />



#### 2025年7月26日更新：

- AugmentCode官方于7月24日更新新用户试用规则，新用户免费试用7天，调用次数变为125次
- 7月26日官方封控加强，限制无限邮箱+部分国内邮箱，建议使用自定义邮箱或者主流邮箱
- 本项目目前依旧正常运行中，如若未来更改规则，无法使用本工具，会通告各位用户，希望各位用户且用且珍惜
- 再次提醒使用本工具前关闭所有有关进程，退出账号，同时邀请策略部分用户已经失效

#### Updated on July 26, 2025:

- AugmentCode officially updated the new user trial rules on July 24, and new users can try for free for 7 days, and the number of calls has been changed to 125 times
- On July 26, the official blockade was strengthened, limiting unlimited mailboxes + some domestic mailboxes. It is recommended to use custom mailboxes or mainstream mailboxes
- This project is still running normally. If the rules are changed in the future and this tool cannot be used, we will notify all users. I hope that all users will use it and cherish it
- Once again remind you to close all related processes and log out of the account before using this tool. At the same time, the invitation policy has expired for some users


#### 2025年7月25日更新 (v1.0.5)：

**🚀 JetBrains 支持**：
- 新增对 JetBrains 系列产品的支持（PyCharm、IntelliJ IDEA、WebStorm、PhpStorm等）
- 实现 SessionID 自动修改功能，支持跨平台路径检测
- 智能配置文件备份和用户设置保护机制

**🔧 功能优化**：
- 改进"一键修改所有配置"工作流程，确保 JetBrains 产品正常运行
- 完善多语言支持，修复英文界面下的文本显示问题
- 优化用户提示信息，为不同IDE类型提供针对性指导

#### 2025年7月25日更新 (v1.0.4)：

**🍎 macOS兼容性**：
- 修复了macOS环境下config_manager和language_manager模块导入失败的问题
- 增强了跨平台路径处理，确保Windows和macOS用户都能直接使用`python main.py`启动
- 添加了自动路径修复机制，当模块导入失败时自动尝试多种路径配置

**🔧 启动优化**：
- 改进了main.py的错误处理和自动恢复功能
- 统一了启动方式，所有平台用户都可以使用相同的命令启动程序

#### 2025年7月25日更新 (v1.0.3)：

**🔧 Bug修复**：
- 修复了About对话框中"支持的IDE"和"主要功能"模块文字显示问题
- 修复了构建系统中的Unicode编码问题，提升了跨平台兼容性

**🧹 代码清理**：
- 清理了无用的测试文件和临时文档
- 统一了版本号，确保所有配置文件版本一致

**📦 发布改进**：
- 优化了构建流程，生成更完整的发布包
- 改进了错误信息显示，使用颜色区分不同类型的消息

#### July 25, 2025 Update (v1.0.5):

**🚀 JetBrains Support**:
- Added support for JetBrains product series (PyCharm, IntelliJ IDEA, WebStorm, PhpStorm, etc.)
- Implemented automatic SessionID modification with cross-platform path detection
- Smart configuration file backup and user settings protection mechanism

**🔧 Feature Optimization**:
- Improved "Modify All Configurations" workflow to ensure JetBrains products work properly
- Enhanced multi-language support, fixed text display issues in English interface
- Optimized user prompts with targeted guidance for different IDE types

#### July 25, 2025 Update (v1.0.4):

**🍎 macOS Compatibility**:
- Fixed config_manager and language_manager module import failures on macOS
- Enhanced cross-platform path handling, ensuring both Windows and macOS users can start with `python main.py`
- Added automatic path repair mechanism that tries multiple path configurations when module import fails

**🔧 Startup Optimization**:
- Improved error handling and auto-recovery functionality in main.py
- Unified startup method, all platform users can use the same command to start the program

#### July 25, 2025 Update (v1.0.3):

**🔧 Bug Fixes**:
- Fixed text display issue in About dialog's "Supported IDEs" and "Main Features" modules
- Fixed Unicode encoding issues in build system, improved cross-platform compatibility

**🧹 Code Cleanup**:
- Cleaned up unused test files and temporary documents
- Unified version numbers across all configuration files

**📦 Release Improvements**:
- Optimized build process to generate more complete release packages
- Improved error message display with color-coded message types

#### 2025年7月23日更新：

2025年7月23日凌晨，官方修改邀请策略：

- 原来的300次请求更改为150次
- 团队依旧可以创建，但是请求次数不叠加
- 我的清理工具依旧可以使用，且支持多IDE；使用前请退出账号关闭IDE
- 部分用户可能由于各种操作原因，清理后账号依旧被防控，此时建议继续使用team邀请的方式，大号创建team，小号加入team，登录IDE使用，尽管请求次数不叠加。

#### Updated on July 23, 2025:

At dawn on July 23, 2025, the official invitation policy was modified:

- The original 300 requests were changed to 150

- Teams can still be created, but the number of requests is not cumulative

- My cleaning tool can still be used and supports multiple IDEs; please log out of the account and close the IDE before use
- Some users may still be controlled after the account is cleaned due to various operational reasons. At this time, it is recommended to continue to use the team invitation method, create a team with a large account, join the team with a small account, and log in to the IDE to use it, although the number of requests is not cumulative.

#### 2025年6月18日更新：

##### 最新思路：

##### team邀请方式：

1.首先准备任意一个账号（被封禁的，试用到期的，用完免费次数的都可以），浏览器打开https://app.augmentcode.com/account/subscription
获取个人中心页面，点击Team，再点击add member添加一个小号作为团队成员。
![image](https://github.com/user-attachments/assets/caa0f8dc-d189-476f-bc3f-84ee0037e03b)


2.这个小号可以用域名邮箱，随意。添加到团队里面后会收到一封激活邮件。
![image](https://github.com/user-attachments/assets/63117ef9-1e9c-4641-99c1-9d3c9f56f435)


3.点击邮件链接https://auth.augmentcode.com/invitations   ，用小号登录激活。按照提示走完流程即可。

4.执行下面的脚本清理本地环境配置，清理完毕后登录刚刚的小号。然后就可以稳定使用了，到目前为止，方法仍然有效。

总结：大号随便什么号，封了的都可以，大号创建team拉小号，小号就可以无限用，注意一定要小号登录augment插件，登录前记得执行清理脚本。

---


# AugmentCode-Free
AugmentCode unlimited free refill plan; new accounts can get 600 free Claude Sonnet 4 calls

**Multi-IDE Support**: Maintenance toolkit for VS Code, Cursor, Windsurf, and JetBrains

Updated on June 18, 2025:

Latest ideas:

Team invitation method:

1. First prepare any account (banned, trial expired, or used up free times), open https://app.augmentcode.com/account/subscription in the browser
Get the personal center page, click Team, and then click add member to add a small account as a team member.
![image](https://github.com/user-attachments/assets/caa0f8dc-d189-476f-bc3f-84ee0037e03b)

2. This small account can use a domain name email, as you like. You will receive an activation email after adding it to the team.
![image](https://github.com/user-attachments/assets/63117ef9-1e9c-4641-99c1-9d3c9f56f435)

3. Click the email link https://auth.augmentcode.com/invitations and log in with the secondary account to activate. Follow the prompts to complete the process.

4. Execute the following script to clean up the local environment configuration, and log in to the secondary account just now after cleaning. Then you can use it stably. So far, the method is still effective.

Summary: The primary account can be any account, even if it is blocked. The primary account can create a team and pull the secondary account, and the secondary account can be used unlimitedly. Note that you must log in to the augmentation plug-in with the secondary account, and remember to execute the cleanup script before logging in.

---

<p align="center">
  <a href="#english">English</a> | <a href="#中文">中文</a>
</p>

---

<a name="english"></a>

# AugmentCode-Free (English)

**AugmentCode-Free** is a Python-based toolkit, now featuring a modern **Graphical User Interface (GUI)** alongside its command-line interface. It's designed to provide maintenance and tweaking utilities for multiple IDEs including VS Code, Cursor, Windsurf, and JetBrains, helping users manage aspects like telemetry, SessionID, and local cache.

## Features

### Core Functionality (Available in CLI & GUI)
-   **Enhanced Database Cleaning**: Cleans specific entries from VS Code, Cursor, and Windsurf local databases with automatic backup and detailed statistics.
-   **Powerful Process Management**: Intelligent IDE process detection and termination with multiple strategies and retry mechanisms.
-   **Advanced File Cleanup**: Physical file deletion with force delete for locked files and recursive workspace storage cleanup.
-   **Multiple Cleanup Modes**:
    - `database_only`: Clean database content only, preserve file structure
    - `file_only`: Delete physical files only, no database modification
    - `hybrid`: Recommended mode, clean database first then delete related files
    - `aggressive`: Force mode, terminate processes and delete all related files
-   **Multi-IDE Telemetry ID Modification**: Helps in resetting or changing telemetry identifiers stored by supported IDEs.
-   **JetBrains SessionID Management**: Automatically modifies SessionID for JetBrains products (PyCharm, IntelliJ IDEA, WebStorm, etc.).
-   **Smart Process Detection**: Automatically detects and manages running IDE processes.

### New GUI Features
-   **Intuitive Interface**: A user-friendly graphical alternative to command-line operations.
-   **IDE Selection**: Choose between VS Code, Cursor, Windsurf, and JetBrains from a dropdown menu.
-   **One-Click Operations**: Easily perform tasks like modifying IDE telemetry IDs, JetBrains SessionID, and cleaning IDE databases with a single click.
-   **Process Management**: Automatically detects and offers to close running IDE instances to ensure operations proceed smoothly.
-   **User Feedback**: Provides clear confirmation dialogs and status messages for all operations.
-   **Modern Design**: Features animated interface elements and intuitive user experience.

## 📥 Installation & Download

### Option 1: Python Package (Recommended)
Install directly from PyPI (coming soon) or from source:

```bash
# Install from source
git clone https://github.com/BasicProtein/AugmentCode-Free.git
cd AugmentCode-Free
pip install .

# Or install dependencies manually
pip install -r requirements.txt
```

### Option 2: Standalone Downloads

#### Windows Users
- **Executable**: Download `AugmentCode-Free-v1.0.6.exe` (34.5 MB)
  - No Python installation required
  - Double-click to run
  - Includes all dependencies

#### Cross-Platform Users
- **Portable Package**: Download `AugmentCode-Free-v1.0.6-Portable.zip` (51.3 KB)
  - Works on Windows, Linux, macOS
  - Requires Python 3.7+
  - Extract and run startup script

#### Developers
- **Source Package**: Download `augment-tools-core-1.0.6.tar.gz` (36.0 KB)
- **Wheel Package**: Download `augment_tools_core-1.0.6-py3-none-any.whl` (36.8 KB)

### 📋 System Requirements
- **Python**: 3.7 or higher (for source/portable versions)
- **Operating System**: Windows 7/10/11, Linux, macOS
- **Memory**: 100MB RAM minimum
- **Storage**: 50MB free space

### 🔐 File Verification
All release files include SHA256, SHA1, and MD5 checksums for integrity verification.
Download `checksums.txt` or `SHA256SUMS` from the release page to verify your downloads.

## 🚀 Usage

### ⚠️ Important Notes
**Before using this tool:**
- ✅ **Log out** of your AugmentCode account
- ✅ **Close all IDE instances** (VS Code, Cursor, Windsurf)
- ✅ **Backup important data** if needed
- ✅ The tool will automatically detect and help close running IDEs

### Method 1: GUI Interface (Recommended)

#### Windows Executable
1. Download `AugmentCode-Free-v1.0.6.exe`
2. Double-click to run
3. Select your IDE from the dropdown
4. Click the desired operation button
5. Follow on-screen instructions

#### Portable Version
1. Download and extract `AugmentCode-Free-v1.0.6-Portable.zip`
2. **Windows**: Double-click `Start-AugmentCode-Free.bat`
3. **Linux/macOS**: Run `./start-augmentcode-free.sh`
4. Use the GUI interface as described above

#### From Source
```bash
# Clone and install
git clone https://github.com/BasicProtein/AugmentCode-Free.git
cd AugmentCode-Free
pip install -r requirements.txt

# Launch GUI
python main.py
```

### Method 2: Command Line Interface

#### After Installation
```bash
# Install the package
pip install augment-tools-core

# Show all available commands
augment-tools --help

# Clean database for specific IDE
augment-tools clean-db --ide vscode
augment-tools clean-db --ide cursor
augment-tools clean-db --ide windsurf

# Modify telemetry IDs / SessionID
augment-tools modify-ids --ide vscode
augment-tools modify-ids --ide cursor
augment-tools modify-ids --ide windsurf
augment-tools modify-ids --ide jetbrains

# Run all operations (clean + modify)
augment-tools run-all --ide vscode
augment-tools run-all --ide cursor
augment-tools run-all --ide windsurf
augment-tools run-all --ide jetbrains

# === v2.0.0 New Enhanced Commands ===

# Enhanced cleanup (recommended hybrid mode)
augment-tools clean-enhanced --ide vscode --mode hybrid

# Aggressive cleanup (force kill processes + delete files)
augment-tools clean-enhanced --ide vscode --mode aggressive --force --kill-processes

# File cleanup only
augment-tools file-cleanup --ide vscode --force

# Check IDE processes
augment-tools check-processes --ide vscode

# Kill IDE processes
augment-tools kill-processes --ide vscode --force
```

-   **Directly (for development/advanced use, from project root):**
    Refer to `augment_tools_core/cli.py` for direct script execution details if needed.

## Disclaimer
Use these tools at your own risk. Always back up important data before running maintenance functions, especially when they modify application files. While backups might be created automatically by some functions, caution is advised.

---

<a name="中文"></a>

# AugmentCode-Free (中文)

**AugmentCode-Free** 是一个基于 Python 的工具包，现已配备现代化的**图形用户界面 (GUI)** 以及原有的命令行界面。它旨在为多个IDE（包括 VS Code、Cursor、Windsurf 和 JetBrains）提供维护和调整实用程序，帮助用户管理遥测数据、SessionID 和本地缓存等方面。

## 功能特性

### 核心功能 (命令行及GUI均可用)
-   **增强数据库清理**: 清理 VS Code、Cursor、Windsurf 本地数据库中的特定条目，支持自动备份和详细统计。
-   **强力进程管理**: 智能检测和终止IDE进程，支持多种终止策略和重试机制。
-   **高级文件清理**: 物理文件删除功能，支持强制删除被锁定的文件和递归清理工作区存储。
-   **多种清理模式**:
    - `database_only`: 仅清理数据库内容，保留文件结构
    - `file_only`: 仅删除物理文件，不修改数据库内容
    - `hybrid`: 推荐模式，先清理数据库内容，再删除相关文件
    - `aggressive`: 激进模式，强制终止进程并删除所有相关文件
-   **多IDE遥测ID修改**: 帮助重置或更改支持的IDE存储的遥测标识符。
-   **JetBrains SessionID管理**: 自动修改 JetBrains 系列产品的 SessionID（PyCharm、IntelliJ IDEA、WebStorm等）。
-   **智能进程检测**: 自动检测和管理正在运行的IDE进程。

### 全新 GUI 特性
-   **直观界面**: 提供用户友好的图形操作界面，作为命令行的替代选择。
-   **IDE选择**: 通过下拉菜单在 VS Code、Cursor、Windsurf 和 JetBrains 之间选择。
-   **一键式操作**: 通过单击即可轻松执行修改IDE遥测ID、JetBrains SessionID、清理IDE数据库等任务。
-   **进程管理**: 自动检测并提示关闭正在运行的IDE实例，以确保操作顺利进行。
-   **用户反馈**: 为所有操作提供清晰的确认对话框和状态消息。
-   **现代化设计**: 具有动画界面元素和直观的用户体验。

## 安装

1.  **克隆仓库:**
    ```bash
    git clone https://github.com/BasicProtein/AugmentCode-Free.git
    cd AugmentCode-Free
    ```
2.  **安装依赖 (如果 `requirements.txt` 或 `setup.py` 中有指定):**
    ```bash
    pip install .
    # 或者
    # pip install -r requirements.txt
    ```

## 使用方法

**⚠️ 重要提醒**：
- 使用前请确保退出AugmentCode账号的登录
- 关闭IDE后台再运行脚本
- 建议先备份重要数据

**⚠️ 重要提醒**：
- 使用前请确保退出AugmentCode账号的登录
- 关闭IDE后台再运行脚本
- 建议先备份重要数据

**⚠️ 重要提醒**：
- 使用前请确保退出AugmentCode账号的登录
- 关闭IDE后台再运行脚本
- 建议先备份重要数据

**（重要的事情说三遍！）**

## 📦 下载与安装 / Download & Installation

### 预编译版本 / Pre-built Releases

从 [GitHub Releases](https://github.com/BasicProtein/AugmentCode-Free/releases) 下载最新版本：

- **Windows**: `AugmentCode-Free-v2.0.4-windows.exe`
- **macOS**: `AugmentCode-Free-v2.0.4-macos.app` 或 `AugmentCode-Free-v2.0.4-macos-standalone`
- **Linux**: `AugmentCode-Free-v2.0.4-linux`

### macOS 用户特别说明 / Special Notes for macOS Users

如果遇到构建或运行问题，请参考：
- 📖 [构建说明 / Build Instructions](https://github.com/BasicProtein/AugmentCode-Free/blob/main/docs/BUILD_INSTRUCTIONS.md)
- 🔧 [macOS 故障排除指南 / macOS Troubleshooting Guide](https://github.com/BasicProtein/AugmentCode-Free/blob/main/docs/MACOS_TROUBLESHOOTING.md)

## 🚀 使用方法 / Usage

您可以通过两种方式使用 AugmentCode-Free：

### 1. 图形用户界面 (GUI) - 推荐 / Graphical User Interface (Recommended)
GUI 为所有功能提供了简单易用的操作界面。

-   **直接运行 (从项目根目录) / Direct Run (from project root):**
    ```bash
    python main.py
    ```

-   **如果通过 pip 安装 (作为 `augment-tools-gui`) / If installed via pip:**
    ```bash
    augment-tools-gui
    ```

### 2. 命令行界面 (CLI) / Command Line Interface
适用于喜欢命令行或需要编写脚本自动执行操作的用户。/ For users who prefer command line or need to automate operations with scripts.

-   **如果通过 pip 安装 (作为 `augment-tools`):**
    ```bash
    # 显示帮助
    augment-tools --help
    
    # 清理 VS Code 数据库
    augment-tools clean-db --ide vscode

    # 清理 Cursor 数据库
    augment-tools clean-db --ide cursor

    # 修改 Windsurf 遥测ID
    augment-tools modify-ids --ide windsurf

    # 修改 JetBrains SessionID
    augment-tools modify-ids --ide jetbrains

    # 为 VS Code 运行所有工具
    augment-tools run-all --ide vscode

    # === v2.0.0 新增增强命令 ===

    # 增强清理 (推荐混合模式)
    augment-tools clean-enhanced --ide vscode --mode hybrid

    # 激进清理 (强制终止进程+删除文件)
    augment-tools clean-enhanced --ide vscode --mode aggressive --force --kill-processes

    # 仅文件清理
    augment-tools file-cleanup --ide vscode --force

    # 检查IDE进程
    augment-tools check-processes --ide vscode

    # 终止IDE进程
    augment-tools kill-processes --ide vscode --force
    ```

-   **直接运行 (用于开发/高级用户, 从项目根目录):**
    如果需要，请参考 `augment_tools_core/cli.py` 了解直接执行脚本的详细信息。

## 免责声明
请自行承担使用这些工具的风险。在运行维护功能前，请务必备份重要数据，尤其是在它们修改应用程序文件时。虽然某些功能可能会自动创建备份，但仍建议谨慎操作。
