('E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\build\\AugmentCode-Free-v2.0.4\\PYZ-00.pyz',
 [('PyQt6',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyQt6\\__init__.py',
   'PYMODULE'),
  ('__future__',
   'D:\\Software\\Python\\Python310\\lib\\__future__.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\Software\\Python\\Python310\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'D:\\Software\\Python\\Python310\\lib\\_compression.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\Software\\Python\\Python310\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal',
   'D:\\Software\\Python\\Python310\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_strptime',
   'D:\\Software\\Python\\Python310\\lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'D:\\Software\\Python\\Python310\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'D:\\Software\\Python\\Python310\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\Software\\Python\\Python310\\lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\Software\\Python\\Python310\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('augment_tools_core',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\augment_tools_core\\__init__.py',
   'PYMODULE'),
  ('augment_tools_core.cleanup_strategies',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\augment_tools_core\\cleanup_strategies.py',
   'PYMODULE'),
  ('augment_tools_core.common_utils',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\augment_tools_core\\common_utils.py',
   'PYMODULE'),
  ('augment_tools_core.database_manager',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\augment_tools_core\\database_manager.py',
   'PYMODULE'),
  ('augment_tools_core.extension_finder',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\augment_tools_core\\extension_finder.py',
   'PYMODULE'),
  ('augment_tools_core.file_cleaner',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\augment_tools_core\\file_cleaner.py',
   'PYMODULE'),
  ('augment_tools_core.jetbrains_manager',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\augment_tools_core\\jetbrains_manager.py',
   'PYMODULE'),
  ('augment_tools_core.patch_manager',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\augment_tools_core\\patch_manager.py',
   'PYMODULE'),
  ('augment_tools_core.process_manager',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\augment_tools_core\\process_manager.py',
   'PYMODULE'),
  ('augment_tools_core.telemetry_manager',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\augment_tools_core\\telemetry_manager.py',
   'PYMODULE'),
  ('base64', 'D:\\Software\\Python\\Python310\\lib\\base64.py', 'PYMODULE'),
  ('bisect', 'D:\\Software\\Python\\Python310\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\Software\\Python\\Python310\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\Software\\Python\\Python310\\lib\\calendar.py', 'PYMODULE'),
  ('colorama',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'D:\\Software\\Python\\Python310\\lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('concurrent',
   'D:\\Software\\Python\\Python310\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\Software\\Python\\Python310\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\Software\\Python\\Python310\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\Software\\Python\\Python310\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\Software\\Python\\Python310\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('config_manager',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\config_manager.py',
   'PYMODULE'),
  ('contextlib',
   'D:\\Software\\Python\\Python310\\lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'D:\\Software\\Python\\Python310\\lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'D:\\Software\\Python\\Python310\\lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\Software\\Python\\Python310\\lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'D:\\Software\\Python\\Python310\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\Software\\Python\\Python310\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\Software\\Python\\Python310\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'D:\\Software\\Python\\Python310\\lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime', 'D:\\Software\\Python\\Python310\\lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\Software\\Python\\Python310\\lib\\decimal.py', 'PYMODULE'),
  ('dis', 'D:\\Software\\Python\\Python310\\lib\\dis.py', 'PYMODULE'),
  ('email',
   'D:\\Software\\Python\\Python310\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\Software\\Python\\Python310\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Software\\Python\\Python310\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\Software\\Python\\Python310\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\Software\\Python\\Python310\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\Software\\Python\\Python310\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\Software\\Python\\Python310\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\Software\\Python\\Python310\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\Software\\Python\\Python310\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\Software\\Python\\Python310\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\Software\\Python\\Python310\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\Software\\Python\\Python310\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\Software\\Python\\Python310\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\Software\\Python\\Python310\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\Software\\Python\\Python310\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'D:\\Software\\Python\\Python310\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\Software\\Python\\Python310\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\Software\\Python\\Python310\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\Software\\Python\\Python310\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\Software\\Python\\Python310\\lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\Software\\Python\\Python310\\lib\\fnmatch.py', 'PYMODULE'),
  ('fractions',
   'D:\\Software\\Python\\Python310\\lib\\fractions.py',
   'PYMODULE'),
  ('ftplib', 'D:\\Software\\Python\\Python310\\lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\Software\\Python\\Python310\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\Software\\Python\\Python310\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\Software\\Python\\Python310\\lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\Software\\Python\\Python310\\lib\\glob.py', 'PYMODULE'),
  ('gui_qt6',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\gui_qt6\\__init__.py',
   'PYMODULE'),
  ('gui_qt6.about_dialog',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\gui_qt6\\about_dialog.py',
   'PYMODULE'),
  ('gui_qt6.components',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\gui_qt6\\components.py',
   'PYMODULE'),
  ('gui_qt6.font_manager',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\gui_qt6\\font_manager.py',
   'PYMODULE'),
  ('gui_qt6.main_page',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\gui_qt6\\main_page.py',
   'PYMODULE'),
  ('gui_qt6.main_window',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\gui_qt6\\main_window.py',
   'PYMODULE'),
  ('gui_qt6.patch_worker',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\gui_qt6\\patch_worker.py',
   'PYMODULE'),
  ('gui_qt6.styles',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\gui_qt6\\styles.py',
   'PYMODULE'),
  ('gui_qt6.welcome_page',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\gui_qt6\\welcome_page.py',
   'PYMODULE'),
  ('gui_qt6.workers',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\gui_qt6\\workers.py',
   'PYMODULE'),
  ('gzip', 'D:\\Software\\Python\\Python310\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\Software\\Python\\Python310\\lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\Software\\Python\\Python310\\lib\\hmac.py', 'PYMODULE'),
  ('http',
   'D:\\Software\\Python\\Python310\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'D:\\Software\\Python\\Python310\\lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'D:\\Software\\Python\\Python310\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('importlib',
   'D:\\Software\\Python\\Python310\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\Software\\Python\\Python310\\lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Software\\Python\\Python310\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Software\\Python\\Python310\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\Software\\Python\\Python310\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\Software\\Python\\Python310\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Software\\Python\\Python310\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\Software\\Python\\Python310\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\Software\\Python\\Python310\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\Software\\Python\\Python310\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\Software\\Python\\Python310\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\Software\\Python\\Python310\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\Software\\Python\\Python310\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\Software\\Python\\Python310\\lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\Software\\Python\\Python310\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'D:\\Software\\Python\\Python310\\lib\\inspect.py', 'PYMODULE'),
  ('json',
   'D:\\Software\\Python\\Python310\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\Software\\Python\\Python310\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\Software\\Python\\Python310\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\Software\\Python\\Python310\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('language_manager',
   'E:\\Data\\Own\\Entrepreneurship\\aug-cursorpro\\AugmentCode-Free\\language_manager.py',
   'PYMODULE'),
  ('logging',
   'D:\\Software\\Python\\Python310\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'D:\\Software\\Python\\Python310\\lib\\lzma.py', 'PYMODULE'),
  ('mimetypes',
   'D:\\Software\\Python\\Python310\\lib\\mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\Software\\Python\\Python310\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\Software\\Python\\Python310\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path',
   'D:\\Software\\Python\\Python310\\lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers', 'D:\\Software\\Python\\Python310\\lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\Software\\Python\\Python310\\lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'D:\\Software\\Python\\Python310\\lib\\optparse.py', 'PYMODULE'),
  ('pathlib', 'D:\\Software\\Python\\Python310\\lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'D:\\Software\\Python\\Python310\\lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'D:\\Software\\Python\\Python310\\lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\Software\\Python\\Python310\\lib\\platform.py', 'PYMODULE'),
  ('pprint', 'D:\\Software\\Python\\Python310\\lib\\pprint.py', 'PYMODULE'),
  ('psutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\psutil\\_compat.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile',
   'D:\\Software\\Python\\Python310\\lib\\py_compile.py',
   'PYMODULE'),
  ('queue', 'D:\\Software\\Python\\Python310\\lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\Software\\Python\\Python310\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\Software\\Python\\Python310\\lib\\random.py', 'PYMODULE'),
  ('runpy', 'D:\\Software\\Python\\Python310\\lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\Software\\Python\\Python310\\lib\\secrets.py', 'PYMODULE'),
  ('selectors',
   'D:\\Software\\Python\\Python310\\lib\\selectors.py',
   'PYMODULE'),
  ('shlex', 'D:\\Software\\Python\\Python310\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\Software\\Python\\Python310\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\Software\\Python\\Python310\\lib\\signal.py', 'PYMODULE'),
  ('socket', 'D:\\Software\\Python\\Python310\\lib\\socket.py', 'PYMODULE'),
  ('sqlite3',
   'D:\\Software\\Python\\Python310\\lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'D:\\Software\\Python\\Python310\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'D:\\Software\\Python\\Python310\\lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl', 'D:\\Software\\Python\\Python310\\lib\\ssl.py', 'PYMODULE'),
  ('statistics',
   'D:\\Software\\Python\\Python310\\lib\\statistics.py',
   'PYMODULE'),
  ('string', 'D:\\Software\\Python\\Python310\\lib\\string.py', 'PYMODULE'),
  ('stringprep',
   'D:\\Software\\Python\\Python310\\lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'D:\\Software\\Python\\Python310\\lib\\subprocess.py',
   'PYMODULE'),
  ('tarfile', 'D:\\Software\\Python\\Python310\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\Software\\Python\\Python310\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\Software\\Python\\Python310\\lib\\textwrap.py', 'PYMODULE'),
  ('threading',
   'D:\\Software\\Python\\Python310\\lib\\threading.py',
   'PYMODULE'),
  ('token', 'D:\\Software\\Python\\Python310\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\Software\\Python\\Python310\\lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc',
   'D:\\Software\\Python\\Python310\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('typing', 'D:\\Software\\Python\\Python310\\lib\\typing.py', 'PYMODULE'),
  ('urllib',
   'D:\\Software\\Python\\Python310\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\Software\\Python\\Python310\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\Software\\Python\\Python310\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\Software\\Python\\Python310\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'D:\\Software\\Python\\Python310\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('uu', 'D:\\Software\\Python\\Python310\\lib\\uu.py', 'PYMODULE'),
  ('uuid', 'D:\\Software\\Python\\Python310\\lib\\uuid.py', 'PYMODULE'),
  ('webbrowser',
   'D:\\Software\\Python\\Python310\\lib\\webbrowser.py',
   'PYMODULE'),
  ('xml', 'D:\\Software\\Python\\Python310\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.etree',
   'D:\\Software\\Python\\Python310\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\Software\\Python\\Python310\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\Software\\Python\\Python310\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\Software\\Python\\Python310\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\Software\\Python\\Python310\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\Software\\Python\\Python310\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\Software\\Python\\Python310\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\Software\\Python\\Python310\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\Software\\Python\\Python310\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\Software\\Python\\Python310\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\Software\\Python\\Python310\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\Software\\Python\\Python310\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\Software\\Python\\Python310\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'D:\\Software\\Python\\Python310\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\Software\\Python\\Python310\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile', 'D:\\Software\\Python\\Python310\\lib\\zipfile.py', 'PYMODULE'),
  ('zipimport',
   'D:\\Software\\Python\\Python310\\lib\\zipimport.py',
   'PYMODULE')])
